import { NextApiRequest, NextApiResponse } from 'next';

import { AuthViaTokenResponse } from '~/data/models/AuthViaToken';
import { backendBootstrap } from '~/lib/backend/bootstrap-legacy';
import {
  backendPostAuthViaClientToken,
  backendPutAuthViaClientToken,
} from '~/lib/backend/checkout/auth-via-token';
import { ErrorResponse } from '~/lib/fetch-backend/index.types';

const authViaTokenApi = async (
  request: NextApiRequest,
  response: NextApiResponse<AuthViaTokenResponse | ErrorResponse>,
) => {
  backendBootstrap({ request });

  switch (request.method?.toLowerCase()) {
    case 'post': {
      const res = await backendPostAuthViaClientToken();

      if (res.isSuccess) {
        return response.json(res.data);
      }

      if (res.error) {
        return response.status(res.error.statusCode).end();
      }
      return response.status(500).end();
    }
    case 'put': {
      const res = await backendPutAuthViaClientToken({ input: request.body });

      if (res.isSuccess) {
        return response.json(res.data);
      }

      if (res.error) {
        return response.status(res.error.statusCode).end();
      }
      return response.status(500).end();
    }
    default:
      response.status(400).end();
      break;
  }
};

export default authViaTokenApi;
