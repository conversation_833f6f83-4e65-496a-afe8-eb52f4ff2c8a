import {
  AuthViaTokenRequest,
  AuthViaTokenResponse,
} from '~/data/models/AuthViaToken';
import { fetchWithErrorHandling } from '~/lib/fetch-backend';

export async function backendPostAuthViaClientToken() {
  return await fetchWithErrorHandling<AuthViaTokenResponse>({
    endpoint: '/v2/site/payment/authvia/customer',
    includeAuthorization: true,
    method: 'post',
  });
}

export async function backendPutAuthViaClientToken({
  input,
}: {
  input: AuthViaTokenRequest;
}) {
  return await fetchWithErrorHandling<
    AuthViaTokenResponse,
    AuthViaTokenRequest
  >({
    endpoint: '/v2/site/payment/authvia/customer',
    includeAuthorization: true,
    jsonBody: input,
    method: 'put',
  });
}
